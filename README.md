# 智能需求管理系统

基于Vue3 + FastAPI + AutoGen的智能需求分析管理系统，具有炫酷的Gemini风格界面。

## 🌟 功能特性

### 1. 仪表盘
- 炫酷的数据可视化界面
- 实时统计项目和需求数据
- 最近活动展示
- 性能指标监控

### 2. 项目管理
- 项目的增删改查操作
- 项目信息包括：项目编号、项目名称、项目内容
- 按创建时间倒序排列
- 支持按项目名称、创建时间查询

### 3. 需求管理

#### 需求分析
- 手动选择所属项目
- 输入需求标题和内容
- 支持导入需求文档（txt、md、doc、docx）
- AI智能分析需求内容
- 实时显示分析前后的需求对比
- 支持重复分析和结果保存

#### 需求列表
- 展示已保存的需求信息
- 支持查看、编辑、删除操作
- 按创建时间倒序排列
- 支持多维度查询：项目名称、需求标题、创建时间等

## 🛠 技术栈

### 前端
- **Vue 3** - 渐进式JavaScript框架
- **Element Plus** - Vue 3组件库
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 快速的前端构建工具
- **ECharts** - 数据可视化图表库
- **Axios** - HTTP客户端
- **Vue Router** - 路由管理
- **Pinia** - 状态管理

### 后端
- **FastAPI** - 现代、快速的Web框架
- **SQLAlchemy** - Python SQL工具包和ORM
- **SQLite** - 轻量级数据库
- **Pydantic** - 数据验证和设置管理
- **AutoGen 0.6.1** - 智能体框架（用于AI需求分析）
- **Uvicorn** - ASGI服务器

## 📁 项目结构

```
myTestProject/
├── frontend/                 # 前端项目
│   ├── src/
│   │   ├── api/             # API接口
│   │   ├── components/      # 通用组件
│   │   ├── router/          # 路由配置
│   │   ├── styles/          # 样式文件
│   │   ├── views/           # 页面组件
│   │   │   ├── Dashboard/   # 仪表盘
│   │   │   ├── Projects/    # 项目管理
│   │   │   ├── Requirements/# 需求管理
│   │   │   └── Layout/      # 布局组件
│   │   ├── App.vue          # 根组件
│   │   └── main.ts          # 应用入口
│   ├── package.json         # 项目配置
│   └── vite.config.ts       # Vite配置
├── backend/                 # 后端项目
│   ├── api/                 # API路由
│   │   ├── dashboard.py     # 仪表盘API
│   │   ├── projects.py      # 项目API
│   │   └── requirements.py  # 需求API
│   ├── services/            # 业务服务
│   │   └── ai_service.py    # AI分析服务
│   ├── models.py            # 数据库模型
│   ├── schemas.py           # Pydantic模型
│   ├── database.py          # 数据库配置
│   ├── crud.py              # 数据库操作
│   ├── main.py              # 应用入口
│   ├── start.py             # 启动脚本
│   └── requirements.txt     # Python依赖
├── example/                 # AutoGen示例
├── start_frontend.sh        # 前端启动脚本
├── start_backend.sh         # 后端启动脚本
└── README.md               # 项目说明
```

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- npm 或 yarn

### 1. 克隆项目
```bash
git clone <repository-url>
cd myTestProject
```

### 2. 启动后端

```bash
# 进入后端目录
cd backend

# 安装Python依赖
pip install -r requirements.txt

# 启动后端服务
python start.py
```

后端服务将在 http://localhost:8000 启动
API文档地址：http://localhost:8000/docs

### 3. 启动前端

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端服务将在 http://localhost:3000 启动

### 4. 使用快速启动脚本

```bash
# 启动后端
chmod +x start_backend.sh
./start_backend.sh

# 启动前端（新终端窗口）
chmod +x start_frontend.sh
./start_frontend.sh
```

## 🎨 界面预览

系统采用Gemini风格设计，具有以下特点：
- 渐变背景和毛玻璃效果
- 现代化的卡片式布局
- 流畅的动画过渡
- 响应式设计，支持移动端
- 深色/浅色主题切换

## 🤖 AI功能

系统集成了AutoGen智能体框架，提供以下AI功能：
- 需求内容智能分析
- 需求质量检查
- 需求优化建议
- 结构化需求文档生成

> 注意：当前版本使用模拟AI服务。要启用真实的AI功能，需要：
> 1. 配置OpenAI API密钥
> 2. 更新 `backend/services/ai_service.py` 中的AutoGen配置
> 3. 确保AutoGen相关依赖正确安装

## 📝 API文档

启动后端服务后，访问 http://localhost:8000/docs 查看完整的API文档。

主要API端点：
- `/api/projects/` - 项目管理
- `/api/requirements/` - 需求管理
- `/api/dashboard/` - 仪表盘数据

## 🔧 开发说明

### 前端开发
- 使用Vue 3 Composition API
- TypeScript提供类型安全
- Element Plus组件库
- 响应式设计，支持多设备

### 后端开发
- FastAPI提供高性能API
- SQLAlchemy ORM管理数据库
- Pydantic进行数据验证
- 模块化设计，易于扩展

### 数据库
- 使用SQLite作为默认数据库
- 支持切换到PostgreSQL、MySQL等
- 自动创建表结构

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 创建 Pull Request

---

**智能需求管理系统** - 让需求分析更智能、更高效！ 🚀
