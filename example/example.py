import asyncio
import re

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.base import TaskResult
from autogen_agentchat.conditions import ExternalTermination, TextMentionTermination, SourceMatchTermination
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, TextMessage
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import Console
from autogen_core import CancellationToken
from autogen_ext.models.openai import OpenAIChatCompletionClient
from utils import model_client


RequirementAnalysisAgent = AssistantAgent(
    "primary",
    model_client=model_client,
    system_message="""
        根据如下格式的需求文档，进行需求分析，输出需求分析报告：
            ## 1. Background
            - **角色定位**: 资深软件测试需求分析师，具备跨领域测试经验  
            - **核心职责**: 将模糊需求转化为可执行的测试方案，识别需求盲区与风险点  
            - **行业经验**: 5年以上金融/医疗/物联网领域测试体系构建经验

            ## 2. Profile
            - **姓名**: TesterBot  
            - **职位**: 智能测试需求架构师  
            - **特质**: 
              - 严谨的逻辑推理能力  
              - 敏锐的边界条件发现能力
              - 优秀的风险预判意识

            ## 3. Skills
            - 掌握ISTQB/敏捷测试方法论  
            - 精通测试用例设计方法（等价类/边界值/场景法等）  
            - 熟练使用JIRA/TestRail/XMind  
            - 擅长需求可测试性评估
            - 精通API/性能/安全测试策略制定

            ## 4. Goals
            1. 解析用户原始需求，明确测试范围
            2. 识别隐含需求与潜在风险点  
            3. 生成结构化测试需求文档
            4. 输出可量化的验收标准
            5. 建立需求追溯矩阵

            ## 5. Constraints
            - 不涉及具体测试代码实现  
            - 不替代人工需求评审  
            - 保持技术中立立场  
            - 遵守ISTQB伦理规范

            ## 6. Output Format
            ```markdown
            # 测试需求分析文档

            ## 测试目标
            - [清晰的功能目标描述]

            ## 需求拆解
            | 需求ID | 需求描述 | 测试类型 | 优先级 | 验收标准 |
            |--------|----------|----------|--------|----------|

            ## 风险分析
            - **高优先级风险**: 
              - [风险描述] → [缓解方案]

            ## 测试策略
            - [功能测试]: 
              - 覆盖场景: 
                - [场景1] 
                - [场景2]
            - [非功能测试]:
              - 性能指标: [RPS ≥ 1000]
              - 安全要求: [OWASP TOP10覆盖]

            ## 待澄清项
            - [问题1] (需业务方确认)
            - [问题2] (需架构师确认)
            ```
        """,
    model_client_stream=True,
)

# Create the critic agent.
critic_agent = AssistantAgent(
    "critic",
    model_client=model_client,
    system_message="""
        # 需求质量验证规范

        ## 验证标准
        1. 模糊术语检查：禁止出现"快速"、"便捷"等不可量化描述，必须转换为具体指标
        2. 验收标准验证：每个需求必须包含≥3个可测试的验收标准
        3. 异常场景覆盖：每个功能必须包含≥1个异常流程处理
        4. 量化指标：性能需求必须包含数字指标（如响应时间≤500ms）

        ## 输出格式
        {
          "valid": true|false,
          "issues": [
            {
              "type": "模糊术语/缺少异常场景/不可验证标准",
              "location": "需求1.验收标准2",
              "original_text": "原始问题内容",
              "suggestion": "具体修改建议（含示例）"
            }
          ],
          "passed_checks": ["检查项名称"]
        }
        """,
    model_client_stream=True,
)


RequirementOutputAgent = AssistantAgent(
    "output",
    model_client=model_client,
    system_message="""
        请根据需求分析报告进行详细的需求整理，尽量覆盖到报告中呈现所有的需求内容，每条需求信息都参考如下格式，生成合适条数的需求项。
        请注意，输出必须是一个有效的JSON格式，不要包含任何解释或前导文本。仅输出JSON对象本身，包含requirements数组。

        生成的JSON格式必须符合这个结构:
        {
          "requirements": [
            {
              "name": "需求名称",
              "description": "作为一名<某类型的用户>，我希望<达成某些目的>，这样可以<开发的价值>。",
              "category": "功能/性能/安全/接口/体验/改进/其它",
              "parent": "该需求的上级需求",
              "module": "所属的业务模块",
              "level": "需求层级[BR]",
              "reviewer": "田威峰/李小明/张美丽",
              "estimated": 8,
              "criteria": "明确的验收标准",
              "remark": "备注信息",
              "keywords": "提取当前需求的关键词，逗号分隔",
              "project_id": 2
            }
          ]
        }

        请确保每个需求项都包含所有必填字段，并且值类型正确。尤其注意estimated必须是数字类型而不是字符串。
        """,
    model_client_stream=True,
)


# 帮我编写一个函数，统计一段文本中的汉子的数量

async def count_chinese_characters(text:  str):
    return len(re.findall(r'[\u4e00-\u9fff]', text))


counter_agent = AssistantAgent(
    "counter",
    model_client=model_client,
    system_message="通过调用工具统计测试用例的字数",
    tools=[count_chinese_characters],
    model_client_stream=True,
)

# Define a termination condition that stops the task if the critic approves.
source_match_termination = SourceMatchTermination(["output"])


# Create a team with the primary and critic agents.
team = RoundRobinGroupChat([RequirementAnalysisAgent, critic_agent, RequirementOutputAgent], termination_condition=source_match_termination)

# Use `asyncio.run(...)` when running in a script.
async def main():
    # Run the team.
    stream = team.run_stream(task="分析一下系统需求：智能系统增加员工管理功能，对员工进行新增、修改、删除")
    async for event in stream:
        if isinstance(event, ModelClientStreamingChunkEvent):   #  输出流，根据source属性判断是哪个agent的输出
            print(event.content,  end="", flush=True)
        if isinstance(event, TextMessage) and event.source == "RequirementAnalysisAgent":
            print(event.content)  # 表示primary智能体最终的完整输出
            break
        if isinstance(event, TextMessage) and event.source == "critic":
            print(event.content)  # 表示critic智能体最终的完整输出
            break
        if isinstance(event, TextMessage) and event.source == "RequirementOutputAgent":
            print(event.content)  # 表示critic智能体最终的完整输出
            break

        if isinstance(event, TaskResult):   # 包含所有智能体的输出，包括用户的输入
            print(event.messages)   # 列表存储，每个元素是一个TextMessage，代表是每个智能体的输出

        print(event)

asyncio.run(main())