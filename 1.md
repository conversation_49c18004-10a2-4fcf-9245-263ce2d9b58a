做一个系统：
前端：vue3, element-plus，页面美观、界面风格参考 gemini，界面风格要炫酷，代码放到frontend目录
后端：python，fastapi, autogen智能体框架0.6.1版本（参考：https://microsoft.github.io/autogen/stable/user-guide/agentchat-user-guide/tutorial/teams.html ；
参考：
https://microsoft.github.io/autogen/stable/user-guide/agentchat-user-guide/tutorial/agents.html ）
autogen相关组件已经完成安装
数据库：dbsqlite，或者你推荐一个合适的数据库
系统主要功能：是利用大模型进行需求细化等
1.仪表盘(炫酷的仪表盘)
2.项目管理：可以新建、查看、编辑、删除项目，项目包括：项目编号、项目名称（不能重复）、项目内容，项目按照创建项目的时间倒序排列，页面上方有查询功能：按照项目名称、项目创建时间等进行查询
3.需求管理：
包含：需求分析、需求列表
需求分析：页面能够新建一条需求：手动选择所属项目名称、输入需求标题、需求内容，可以手动输入或者进行导入需求文档，然后通过AI大模型进行细化需求，页面右侧显示细化前与细化后的需求内容，通过AI分析后的需求内容，如果用户满意，可以进行保存到数据库中，如果不满意，可以重复进行AI分析；
需求列表：对AI分析后保存的需求内容在数据库中的，展现在需求列表中，可以对需求进行查看、编辑、删除等操作，列表按照需求创建的时间进行倒序排列，页面上方有查询功能：按照项目名称、需求标题、需求创建时间等进行查询