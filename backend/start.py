#!/usr/bin/env python3
"""
启动脚本 - 智能需求管理系统后端
"""

import uvicorn
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    print("🚀 启动智能需求管理系统后端服务...")
    print("📍 API文档地址: http://localhost:8000/docs")
    print("📍 前端开发服务器: http://localhost:3000")
    print("=" * 50)
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
