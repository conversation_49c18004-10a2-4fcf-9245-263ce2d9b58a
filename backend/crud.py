from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import Optional, List
from datetime import datetime
import math

from models import Project, Requirement
from schemas import ProjectCreate, ProjectUpdate, RequirementCreate, RequirementUpdate

# 项目相关操作
def get_project(db: Session, project_id: int):
    return db.query(Project).filter(Project.id == project_id).first()

def get_project_by_name(db: Session, name: str):
    return db.query(Project).filter(Project.name == name).first()

def get_project_by_code(db: Session, project_code: str):
    return db.query(Project).filter(Project.project_code == project_code).first()

def get_projects(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    name: Optional[str] = None,
    created_start: Optional[datetime] = None,
    created_end: Optional[datetime] = None
):
    query = db.query(Project)
    
    # 添加过滤条件
    if name:
        query = query.filter(Project.name.contains(name))
    if created_start:
        query = query.filter(Project.created_at >= created_start)
    if created_end:
        query = query.filter(Project.created_at <= created_end)
    
    # 按创建时间倒序排列
    query = query.order_by(Project.created_at.desc())
    
    return query.offset(skip).limit(limit).all()

def get_projects_count(
    db: Session,
    name: Optional[str] = None,
    created_start: Optional[datetime] = None,
    created_end: Optional[datetime] = None
):
    query = db.query(func.count(Project.id))
    
    if name:
        query = query.filter(Project.name.contains(name))
    if created_start:
        query = query.filter(Project.created_at >= created_start)
    if created_end:
        query = query.filter(Project.created_at <= created_end)
    
    return query.scalar()

def create_project(db: Session, project: ProjectCreate):
    db_project = Project(**project.dict())
    db.add(db_project)
    db.commit()
    db.refresh(db_project)
    return db_project

def update_project(db: Session, project_id: int, project: ProjectUpdate):
    db_project = get_project(db, project_id)
    if db_project:
        update_data = project.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_project, field, value)
        db_project.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(db_project)
    return db_project

def delete_project(db: Session, project_id: int):
    db_project = get_project(db, project_id)
    if db_project:
        db.delete(db_project)
        db.commit()
    return db_project

# 需求相关操作
def get_requirement(db: Session, requirement_id: int):
    return db.query(Requirement).filter(Requirement.id == requirement_id).first()

def get_requirements(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    project_id: Optional[int] = None,
    title: Optional[str] = None,
    category: Optional[str] = None,
    status: Optional[str] = None,
    created_start: Optional[datetime] = None,
    created_end: Optional[datetime] = None
):
    query = db.query(Requirement)
    
    # 添加过滤条件
    if project_id:
        query = query.filter(Requirement.project_id == project_id)
    if title:
        query = query.filter(Requirement.title.contains(title))
    if category:
        query = query.filter(Requirement.category == category)
    if status:
        query = query.filter(Requirement.status == status)
    if created_start:
        query = query.filter(Requirement.created_at >= created_start)
    if created_end:
        query = query.filter(Requirement.created_at <= created_end)
    
    # 按创建时间倒序排列
    query = query.order_by(Requirement.created_at.desc())
    
    return query.offset(skip).limit(limit).all()

def get_requirements_count(
    db: Session,
    project_id: Optional[int] = None,
    title: Optional[str] = None,
    category: Optional[str] = None,
    status: Optional[str] = None,
    created_start: Optional[datetime] = None,
    created_end: Optional[datetime] = None
):
    query = db.query(func.count(Requirement.id))
    
    if project_id:
        query = query.filter(Requirement.project_id == project_id)
    if title:
        query = query.filter(Requirement.title.contains(title))
    if category:
        query = query.filter(Requirement.category == category)
    if status:
        query = query.filter(Requirement.status == status)
    if created_start:
        query = query.filter(Requirement.created_at >= created_start)
    if created_end:
        query = query.filter(Requirement.created_at <= created_end)
    
    return query.scalar()

def create_requirement(db: Session, requirement: RequirementCreate):
    db_requirement = Requirement(**requirement.dict())
    db.add(db_requirement)
    db.commit()
    db.refresh(db_requirement)
    return db_requirement

def update_requirement(db: Session, requirement_id: int, requirement: RequirementUpdate):
    db_requirement = get_requirement(db, requirement_id)
    if db_requirement:
        update_data = requirement.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_requirement, field, value)
        db_requirement.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(db_requirement)
    return db_requirement

def delete_requirement(db: Session, requirement_id: int):
    db_requirement = get_requirement(db, requirement_id)
    if db_requirement:
        db.delete(db_requirement)
        db.commit()
    return db_requirement
