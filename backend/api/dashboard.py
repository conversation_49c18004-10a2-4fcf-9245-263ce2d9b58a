from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from datetime import datetime, timedelta
from typing import List, Dict, Any

from database import get_db
from models import Project, Requirement
import crud

router = APIRouter(prefix="/dashboard", tags=["dashboard"])

@router.get("/stats")
def get_dashboard_stats(db: Session = Depends(get_db)):
    """获取仪表盘统计数据"""
    
    # 基础统计
    total_projects = db.query(func.count(Project.id)).scalar()
    total_requirements = db.query(func.count(Requirement.id)).scalar()
    
    # 需求状态统计
    requirement_status_stats = db.query(
        Requirement.status,
        func.count(Requirement.id).label('count')
    ).group_by(Requirement.status).all()
    
    # 需求分类统计
    requirement_category_stats = db.query(
        Requirement.category,
        func.count(Requirement.id).label('count')
    ).group_by(Requirement.category).all()
    
    # 最近7天的项目创建趋势
    seven_days_ago = datetime.utcnow() - timedelta(days=7)
    recent_projects = db.query(
        func.date(Project.created_at).label('date'),
        func.count(Project.id).label('count')
    ).filter(
        Project.created_at >= seven_days_ago
    ).group_by(func.date(Project.created_at)).all()
    
    # 最近7天的需求创建趋势
    recent_requirements = db.query(
        func.date(Requirement.created_at).label('date'),
        func.count(Requirement.id).label('count')
    ).filter(
        Requirement.created_at >= seven_days_ago
    ).group_by(func.date(Requirement.created_at)).all()
    
    # 项目需求分布（每个项目的需求数量）
    project_requirement_distribution = db.query(
        Project.name,
        func.count(Requirement.id).label('requirement_count')
    ).outerjoin(Requirement).group_by(Project.id, Project.name).all()
    
    # 最活跃的项目（最近30天有需求更新的项目）
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    active_projects = db.query(
        Project.name,
        func.count(Requirement.id).label('recent_requirements')
    ).join(Requirement).filter(
        Requirement.updated_at >= thirty_days_ago
    ).group_by(Project.id, Project.name).order_by(
        func.count(Requirement.id).desc()
    ).limit(5).all()
    
    return {
        "basic_stats": {
            "total_projects": total_projects,
            "total_requirements": total_requirements,
            "avg_requirements_per_project": round(total_requirements / total_projects, 2) if total_projects > 0 else 0
        },
        "requirement_status_distribution": [
            {"status": status, "count": count, "label": get_status_label(status)}
            for status, count in requirement_status_stats
        ],
        "requirement_category_distribution": [
            {"category": category or "未分类", "count": count}
            for category, count in requirement_category_stats
        ],
        "recent_trends": {
            "projects": [
                {"date": str(date), "count": count}
                for date, count in recent_projects
            ],
            "requirements": [
                {"date": str(date), "count": count}
                for date, count in recent_requirements
            ]
        },
        "project_requirement_distribution": [
            {"project_name": name, "requirement_count": count}
            for name, count in project_requirement_distribution
        ],
        "active_projects": [
            {"project_name": name, "recent_requirements": count}
            for name, count in active_projects
        ]
    }

@router.get("/recent-activities")
def get_recent_activities(limit: int = 10, db: Session = Depends(get_db)):
    """获取最近活动"""
    
    # 最近创建的项目
    recent_projects = db.query(Project).order_by(
        Project.created_at.desc()
    ).limit(limit // 2).all()
    
    # 最近更新的需求
    recent_requirements = db.query(Requirement).order_by(
        Requirement.updated_at.desc()
    ).limit(limit // 2).all()
    
    activities = []
    
    # 添加项目活动
    for project in recent_projects:
        activities.append({
            "type": "project_created",
            "title": f"创建了项目: {project.name}",
            "description": project.content[:100] + "..." if project.content and len(project.content) > 100 else project.content,
            "timestamp": project.created_at,
            "entity_id": project.id,
            "entity_type": "project"
        })
    
    # 添加需求活动
    for requirement in recent_requirements:
        activity_type = "requirement_updated" if requirement.analyzed_content else "requirement_created"
        title = f"{'更新了需求' if requirement.analyzed_content else '创建了需求'}: {requirement.title}"
        
        activities.append({
            "type": activity_type,
            "title": title,
            "description": requirement.original_content[:100] + "..." if len(requirement.original_content) > 100 else requirement.original_content,
            "timestamp": requirement.updated_at,
            "entity_id": requirement.id,
            "entity_type": "requirement",
            "project_name": requirement.project.name
        })
    
    # 按时间排序
    activities.sort(key=lambda x: x["timestamp"], reverse=True)
    
    return {
        "activities": activities[:limit]
    }

@router.get("/performance-metrics")
def get_performance_metrics(db: Session = Depends(get_db)):
    """获取性能指标"""
    
    # 需求分析效率（有分析内容的需求比例）
    total_requirements = db.query(func.count(Requirement.id)).scalar()
    analyzed_requirements = db.query(func.count(Requirement.id)).filter(
        Requirement.analyzed_content.isnot(None)
    ).scalar()
    
    analysis_rate = (analyzed_requirements / total_requirements * 100) if total_requirements > 0 else 0
    
    # 平均需求处理时间（从创建到分析完成）
    analyzed_reqs = db.query(Requirement).filter(
        Requirement.analyzed_content.isnot(None)
    ).all()
    
    processing_times = []
    for req in analyzed_reqs:
        time_diff = (req.updated_at - req.created_at).total_seconds() / 3600  # 转换为小时
        processing_times.append(time_diff)
    
    avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
    
    # 项目活跃度（最近30天有更新的项目比例）
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    total_projects = db.query(func.count(Project.id)).scalar()
    active_projects_count = db.query(func.count(func.distinct(Requirement.project_id))).filter(
        Requirement.updated_at >= thirty_days_ago
    ).scalar()
    
    project_activity_rate = (active_projects_count / total_projects * 100) if total_projects > 0 else 0
    
    return {
        "analysis_efficiency": {
            "rate": round(analysis_rate, 2),
            "analyzed_count": analyzed_requirements,
            "total_count": total_requirements
        },
        "average_processing_time": {
            "hours": round(avg_processing_time, 2),
            "description": "从需求创建到分析完成的平均时间"
        },
        "project_activity": {
            "rate": round(project_activity_rate, 2),
            "active_count": active_projects_count,
            "total_count": total_projects
        }
    }

def get_status_label(status: str) -> str:
    """获取状态的中文标签"""
    status_map = {
        "draft": "草稿",
        "analyzed": "已分析", 
        "approved": "已批准",
        "in_progress": "开发中",
        "completed": "已完成",
        "rejected": "已拒绝"
    }
    return status_map.get(status, status)
