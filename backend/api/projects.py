from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import datetime
import math

from database import get_db
from schemas import (
    Project, ProjectCreate, ProjectUpdate, ProjectWithRequirements,
    PaginatedResponse, ProjectQuery
)
import crud

router = APIRouter(prefix="/projects", tags=["projects"])

@router.post("/", response_model=Project)
def create_project(project: ProjectCreate, db: Session = Depends(get_db)):
    """创建新项目"""
    # 检查项目名称是否已存在
    if crud.get_project_by_name(db, project.name):
        raise HTTPException(status_code=400, detail="项目名称已存在")
    
    # 检查项目编号是否已存在
    if crud.get_project_by_code(db, project.project_code):
        raise HTTPException(status_code=400, detail="项目编号已存在")
    
    return crud.create_project(db=db, project=project)

@router.get("/", response_model=PaginatedResponse)
def get_projects(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    name: Optional[str] = Query(None, description="项目名称（模糊搜索）"),
    created_start: Optional[datetime] = Query(None, description="创建时间开始"),
    created_end: Optional[datetime] = Query(None, description="创建时间结束"),
    db: Session = Depends(get_db)
):
    """获取项目列表（分页）"""
    skip = (page - 1) * size
    
    projects = crud.get_projects(
        db=db,
        skip=skip,
        limit=size,
        name=name,
        created_start=created_start,
        created_end=created_end
    )
    
    total = crud.get_projects_count(
        db=db,
        name=name,
        created_start=created_start,
        created_end=created_end
    )
    
    pages = math.ceil(total / size) if total > 0 else 1
    
    return PaginatedResponse(
        items=[Project.from_orm(project).dict() for project in projects],
        total=total,
        page=page,
        size=size,
        pages=pages
    )

@router.get("/all", response_model=List[Project])
def get_all_projects(db: Session = Depends(get_db)):
    """获取所有项目（不分页，用于下拉选择）"""
    projects = crud.get_projects(db=db, skip=0, limit=1000)
    return projects

@router.get("/{project_id}", response_model=ProjectWithRequirements)
def get_project(project_id: int, db: Session = Depends(get_db)):
    """获取单个项目详情"""
    project = crud.get_project(db=db, project_id=project_id)
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    return project

@router.put("/{project_id}", response_model=Project)
def update_project(
    project_id: int,
    project: ProjectUpdate,
    db: Session = Depends(get_db)
):
    """更新项目"""
    db_project = crud.get_project(db=db, project_id=project_id)
    if not db_project:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    # 如果更新名称，检查是否与其他项目重复
    if project.name and project.name != db_project.name:
        existing_project = crud.get_project_by_name(db, project.name)
        if existing_project and existing_project.id != project_id:
            raise HTTPException(status_code=400, detail="项目名称已存在")
    
    return crud.update_project(db=db, project_id=project_id, project=project)

@router.delete("/{project_id}")
def delete_project(project_id: int, db: Session = Depends(get_db)):
    """删除项目"""
    project = crud.get_project(db=db, project_id=project_id)
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    crud.delete_project(db=db, project_id=project_id)
    return {"message": "项目删除成功"}

@router.get("/{project_id}/stats")
def get_project_stats(project_id: int, db: Session = Depends(get_db)):
    """获取项目统计信息"""
    project = crud.get_project(db=db, project_id=project_id)
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    # 统计需求数量
    total_requirements = crud.get_requirements_count(db=db, project_id=project_id)
    draft_requirements = crud.get_requirements_count(db=db, project_id=project_id, status="draft")
    analyzed_requirements = crud.get_requirements_count(db=db, project_id=project_id, status="analyzed")
    completed_requirements = crud.get_requirements_count(db=db, project_id=project_id, status="completed")
    
    return {
        "project_id": project_id,
        "project_name": project.name,
        "total_requirements": total_requirements,
        "draft_requirements": draft_requirements,
        "analyzed_requirements": analyzed_requirements,
        "completed_requirements": completed_requirements,
        "created_at": project.created_at,
        "updated_at": project.updated_at
    }
