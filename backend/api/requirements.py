from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import datetime
import math
import json

from database import get_db
from schemas import (
    Requirement, RequirementCreate, RequirementUpdate, RequirementWithProject,
    RequirementAnalysisRequest, RequirementAnalysisResponse,
    PaginatedResponse
)
import crud
from services.ai_service import ai_service

router = APIRouter(prefix="/requirements", tags=["requirements"])

@router.post("/", response_model=Requirement)
def create_requirement(requirement: RequirementCreate, db: Session = Depends(get_db)):
    """创建新需求"""
    # 检查项目是否存在
    project = crud.get_project(db, requirement.project_id)
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    return crud.create_requirement(db=db, requirement=requirement)

@router.get("/", response_model=PaginatedResponse)
def get_requirements(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    project_id: Optional[int] = Query(None, description="项目ID"),
    title: Optional[str] = Query(None, description="需求标题（模糊搜索）"),
    category: Optional[str] = Query(None, description="需求分类"),
    status: Optional[str] = Query(None, description="需求状态"),
    created_start: Optional[datetime] = Query(None, description="创建时间开始"),
    created_end: Optional[datetime] = Query(None, description="创建时间结束"),
    db: Session = Depends(get_db)
):
    """获取需求列表（分页）"""
    skip = (page - 1) * size
    
    requirements = crud.get_requirements(
        db=db,
        skip=skip,
        limit=size,
        project_id=project_id,
        title=title,
        category=category,
        status=status,
        created_start=created_start,
        created_end=created_end
    )
    
    total = crud.get_requirements_count(
        db=db,
        project_id=project_id,
        title=title,
        category=category,
        status=status,
        created_start=created_start,
        created_end=created_end
    )
    
    pages = math.ceil(total / size) if total > 0 else 1
    
    # 包含项目信息
    items = []
    for req in requirements:
        req_dict = Requirement.from_orm(req).dict()
        req_dict['project'] = {
            'id': req.project.id,
            'name': req.project.name,
            'project_code': req.project.project_code
        }
        items.append(req_dict)
    
    return PaginatedResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        pages=pages
    )

@router.get("/{requirement_id}", response_model=RequirementWithProject)
def get_requirement(requirement_id: int, db: Session = Depends(get_db)):
    """获取单个需求详情"""
    requirement = crud.get_requirement(db=db, requirement_id=requirement_id)
    if not requirement:
        raise HTTPException(status_code=404, detail="需求不存在")
    return requirement

@router.put("/{requirement_id}", response_model=Requirement)
def update_requirement(
    requirement_id: int,
    requirement: RequirementUpdate,
    db: Session = Depends(get_db)
):
    """更新需求"""
    db_requirement = crud.get_requirement(db=db, requirement_id=requirement_id)
    if not db_requirement:
        raise HTTPException(status_code=404, detail="需求不存在")
    
    return crud.update_requirement(db=db, requirement_id=requirement_id, requirement=requirement)

@router.delete("/{requirement_id}")
def delete_requirement(requirement_id: int, db: Session = Depends(get_db)):
    """删除需求"""
    requirement = crud.get_requirement(db=db, requirement_id=requirement_id)
    if not requirement:
        raise HTTPException(status_code=404, detail="需求不存在")
    
    crud.delete_requirement(db=db, requirement_id=requirement_id)
    return {"message": "需求删除成功"}

@router.post("/analyze", response_model=RequirementAnalysisResponse)
async def analyze_requirement(request: RequirementAnalysisRequest):
    """AI分析需求"""
    try:
        result = await ai_service.analyze_requirement(request.content)
        
        if result["success"]:
            return RequirementAnalysisResponse(
                original_content=result["original_content"],
                analyzed_content=result["optimized_content"],
                suggestions=result.get("suggestions", [])
            )
        else:
            raise HTTPException(status_code=500, detail=f"AI分析失败: {result.get('error', '未知错误')}")
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分析过程中发生错误: {str(e)}")

@router.post("/{requirement_id}/save-analysis")
def save_analysis_result(
    requirement_id: int,
    analyzed_content: str,
    db: Session = Depends(get_db)
):
    """保存AI分析结果"""
    requirement = crud.get_requirement(db=db, requirement_id=requirement_id)
    if not requirement:
        raise HTTPException(status_code=404, detail="需求不存在")
    
    update_data = RequirementUpdate(
        analyzed_content=analyzed_content,
        status="analyzed"
    )
    
    updated_requirement = crud.update_requirement(
        db=db, 
        requirement_id=requirement_id, 
        requirement=update_data
    )
    
    return {"message": "分析结果保存成功", "requirement": updated_requirement}

@router.post("/upload-document")
async def upload_requirement_document(file: UploadFile = File(...)):
    """上传需求文档"""
    if not file.filename.endswith(('.txt', '.md', '.doc', '.docx')):
        raise HTTPException(status_code=400, detail="仅支持txt、md、doc、docx格式的文件")
    
    try:
        content = await file.read()
        
        # 简单的文本提取（实际项目中可能需要更复杂的文档解析）
        if file.filename.endswith('.txt') or file.filename.endswith('.md'):
            text_content = content.decode('utf-8')
        else:
            # 对于doc/docx文件，这里简化处理
            # 实际项目中可以使用python-docx等库来解析
            text_content = "文档内容解析功能待完善，请手动输入需求内容"
        
        return {
            "filename": file.filename,
            "content": text_content,
            "message": "文档上传成功"
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文档处理失败: {str(e)}")

@router.get("/categories/list")
def get_requirement_categories():
    """获取需求分类列表"""
    return {
        "categories": [
            "功能需求",
            "性能需求", 
            "安全需求",
            "接口需求",
            "用户体验",
            "系统改进",
            "其他"
        ]
    }

@router.get("/status/list")
def get_requirement_status():
    """获取需求状态列表"""
    return {
        "status": [
            {"value": "draft", "label": "草稿"},
            {"value": "analyzed", "label": "已分析"},
            {"value": "approved", "label": "已批准"},
            {"value": "in_progress", "label": "开发中"},
            {"value": "completed", "label": "已完成"},
            {"value": "rejected", "label": "已拒绝"}
        ]
    }
