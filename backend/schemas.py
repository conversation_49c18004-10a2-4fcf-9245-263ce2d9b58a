from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

# 项目相关模型
class ProjectBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=200, description="项目名称")
    content: Optional[str] = Field(None, description="项目内容")

class ProjectCreate(ProjectBase):
    project_code: str = Field(..., min_length=1, max_length=50, description="项目编号")

class ProjectUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    content: Optional[str] = None

class Project(ProjectBase):
    id: int
    project_code: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class ProjectWithRequirements(Project):
    requirements: List['Requirement'] = []

# 需求相关模型
class RequirementBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=500, description="需求标题")
    original_content: str = Field(..., min_length=1, description="原始需求内容")
    category: Optional[str] = Field(None, max_length=50, description="需求分类")
    priority: Optional[str] = Field("medium", description="优先级")

class RequirementCreate(RequirementBase):
    project_id: int = Field(..., description="所属项目ID")

class RequirementUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=500)
    original_content: Optional[str] = Field(None, min_length=1)
    analyzed_content: Optional[str] = None
    category: Optional[str] = None
    priority: Optional[str] = None
    status: Optional[str] = None

class Requirement(RequirementBase):
    id: int
    project_id: int
    analyzed_content: Optional[str] = None
    status: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class RequirementWithProject(Requirement):
    project: Project

# AI分析相关模型
class RequirementAnalysisRequest(BaseModel):
    content: str = Field(..., min_length=1, description="需求内容")
    project_id: Optional[int] = Field(None, description="项目ID")

class RequirementAnalysisResponse(BaseModel):
    original_content: str
    analyzed_content: str
    suggestions: Optional[List[str]] = None

# 查询参数模型
class ProjectQuery(BaseModel):
    name: Optional[str] = None
    created_start: Optional[datetime] = None
    created_end: Optional[datetime] = None
    page: int = Field(1, ge=1)
    size: int = Field(10, ge=1, le=100)

class RequirementQuery(BaseModel):
    project_name: Optional[str] = None
    title: Optional[str] = None
    category: Optional[str] = None
    status: Optional[str] = None
    created_start: Optional[datetime] = None
    created_end: Optional[datetime] = None
    page: int = Field(1, ge=1)
    size: int = Field(10, ge=1, le=100)

# 响应模型
class PaginatedResponse(BaseModel):
    items: List[dict]
    total: int
    page: int
    size: int
    pages: int
