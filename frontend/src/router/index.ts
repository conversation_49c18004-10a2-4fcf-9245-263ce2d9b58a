import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import NProgress from 'nprogress'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/views/Layout/index.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard/index.vue'),
        meta: {
          title: '仪表盘',
          icon: 'DataBoard'
        }
      },
      {
        path: '/projects',
        name: 'Projects',
        component: () => import('@/views/Projects/index.vue'),
        meta: {
          title: '项目管理',
          icon: 'FolderOpened'
        }
      },
      {
        path: '/requirements',
        name: 'Requirements',
        redirect: '/requirements/analysis',
        meta: {
          title: '需求管理',
          icon: 'Document'
        },
        children: [
          {
            path: '/requirements/analysis',
            name: 'RequirementAnalysis',
            component: () => import('@/views/Requirements/Analysis.vue'),
            meta: {
              title: '需求分析',
              icon: 'MagicStick'
            }
          },
          {
            path: '/requirements/list',
            name: 'RequirementList',
            component: () => import('@/views/Requirements/List.vue'),
            meta: {
              title: '需求列表',
              icon: 'List'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/Error/404.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  NProgress.start()
  
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 智能需求管理系统`
  } else {
    document.title = '智能需求管理系统'
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
