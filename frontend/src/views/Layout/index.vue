<template>
  <div class="layout-container">
    <!-- 侧边栏 -->
    <aside class="sidebar" :class="{ collapsed: isCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <el-icon class="logo-icon"><MagicStick /></el-icon>
          <span v-show="!isCollapsed" class="logo-text">智能需求管理</span>
        </div>
        <el-button 
          class="collapse-btn" 
          :icon="isCollapsed ? 'Expand' : 'Fold'"
          @click="toggleCollapse"
          text
        />
      </div>
      
      <nav class="sidebar-nav">
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapsed"
          :unique-opened="true"
          router
          background-color="transparent"
          text-color="#ffffff"
          active-text-color="#ffffff"
        >
          <el-menu-item index="/dashboard">
            <el-icon><DataBoard /></el-icon>
            <span>仪表盘</span>
          </el-menu-item>
          
          <el-menu-item index="/projects">
            <el-icon><FolderOpened /></el-icon>
            <span>项目管理</span>
          </el-menu-item>
          
          <el-sub-menu index="/requirements">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>需求管理</span>
            </template>
            <el-menu-item index="/requirements/analysis">
              <el-icon><MagicStick /></el-icon>
              <span>需求分析</span>
            </el-menu-item>
            <el-menu-item index="/requirements/list">
              <el-icon><List /></el-icon>
              <span>需求列表</span>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </nav>
    </aside>

    <!-- 主内容区 -->
    <div class="main-container">
      <!-- 顶部导航 -->
      <header class="header">
        <div class="header-left">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item 
              v-for="item in breadcrumbs" 
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <el-button :icon="isDark ? 'Sunny' : 'Moon'" @click="toggleTheme" circle />
          <el-dropdown>
            <el-avatar :size="32" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>个人设置</el-dropdown-item>
                <el-dropdown-item divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="content">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useDark, useToggle } from '@vueuse/core'

const route = useRoute()
const isCollapsed = ref(false)
const isDark = useDark()
const toggleTheme = useToggle(isDark)

// 当前激活的菜单
const activeMenu = computed(() => {
  const path = route.path
  if (path.startsWith('/requirements')) {
    return path
  }
  return path
})

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  const breadcrumbList = matched.map(item => ({
    title: item.meta?.title as string,
    path: item.path
  }))
  
  // 添加首页
  if (breadcrumbList.length > 0 && breadcrumbList[0].path !== '/dashboard') {
    breadcrumbList.unshift({ title: '首页', path: '/dashboard' })
  }
  
  return breadcrumbList
})

// 切换侧边栏折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

// 监听路由变化，自动收起移动端侧边栏
watch(() => route.path, () => {
  if (window.innerWidth <= 768) {
    isCollapsed.value = true
  }
})
</script>

<style lang="scss" scoped>
.layout-container {
  display: flex;
  height: 100vh;
  background: var(--app-bg-gradient);
}

.sidebar {
  width: 240px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  transition: width 0.3s ease;
  
  &.collapsed {
    width: 64px;
  }
  
  .sidebar-header {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    
    .logo {
      display: flex;
      align-items: center;
      color: white;
      font-weight: 600;
      
      .logo-icon {
        font-size: 24px;
        margin-right: 8px;
      }
      
      .logo-text {
        font-size: 16px;
        white-space: nowrap;
      }
    }
    
    .collapse-btn {
      color: white;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }
  
  .sidebar-nav {
    padding: 16px 0;
    
    :deep(.el-menu) {
      border: none;
      
      .el-menu-item,
      .el-sub-menu__title {
        height: 48px;
        line-height: 48px;
        margin: 4px 12px;
        border-radius: 8px;
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
        
        &.is-active {
          background: rgba(255, 255, 255, 0.2);
          color: white !important;
        }
      }
      
      .el-sub-menu .el-menu-item {
        margin: 2px 12px;
        padding-left: 48px !important;
      }
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  
  .header-left {
    .el-breadcrumb {
      font-size: 14px;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}

.content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.05);
}

// 响应式
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    height: 100vh;
    
    &.collapsed {
      transform: translateX(-100%);
    }
  }
  
  .main-container {
    margin-left: 0;
  }
}
</style>
