<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="stat in statsCards" :key="stat.key">
        <div class="stat-icon" :class="stat.iconClass">
          <el-icon :size="32"><component :is="stat.icon" /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <!-- 需求状态分布 -->
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>需求状态分布</span>
            <el-button type="primary" size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>
        <div class="chart-container" ref="statusChartRef"></div>
      </el-card>

      <!-- 项目需求分布 -->
      <el-card class="chart-card">
        <template #header>
          <span>项目需求分布</span>
        </template>
        <div class="chart-container" ref="projectChartRef"></div>
      </el-card>

      <!-- 最近趋势 -->
      <el-card class="chart-card full-width">
        <template #header>
          <span>最近7天趋势</span>
        </template>
        <div class="chart-container" ref="trendChartRef"></div>
      </el-card>
    </div>

    <!-- 最近活动 -->
    <el-card class="activity-card">
      <template #header>
        <span>最近活动</span>
      </template>
      <div class="activity-list">
        <div 
          v-for="activity in recentActivities" 
          :key="activity.timestamp"
          class="activity-item"
        >
          <div class="activity-icon">
            <el-icon>
              <component :is="getActivityIcon(activity.type)" />
            </el-icon>
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-desc">{{ activity.description }}</div>
            <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { dashboardApi, type DashboardStats, type RecentActivity } from '@/api/dashboard'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 图表引用
const statusChartRef = ref<HTMLElement>()
const projectChartRef = ref<HTMLElement>()
const trendChartRef = ref<HTMLElement>()

// 数据
const dashboardData = ref<DashboardStats>()
const recentActivities = ref<RecentActivity[]>([])
const loading = ref(false)

// 统计卡片数据
const statsCards = computed(() => [
  {
    key: 'projects',
    label: '项目总数',
    value: dashboardData.value?.basic_stats.total_projects || 0,
    icon: 'FolderOpened',
    iconClass: 'primary'
  },
  {
    key: 'requirements',
    label: '需求总数',
    value: dashboardData.value?.basic_stats.total_requirements || 0,
    icon: 'Document',
    iconClass: 'success'
  },
  {
    key: 'average',
    label: '平均需求数',
    value: dashboardData.value?.basic_stats.avg_requirements_per_project || 0,
    icon: 'TrendCharts',
    iconClass: 'warning'
  },
  {
    key: 'analyzed',
    label: '已分析需求',
    value: dashboardData.value?.requirement_status_distribution.find(item => item.status === 'analyzed')?.count || 0,
    icon: 'MagicStick',
    iconClass: 'info'
  }
])

// 获取仪表盘数据
const fetchDashboardData = async () => {
  try {
    loading.value = true
    const [statsRes, activitiesRes] = await Promise.all([
      dashboardApi.getStats(),
      dashboardApi.getRecentActivities(10)
    ])
    
    dashboardData.value = statsRes.data
    recentActivities.value = activitiesRes.data.activities
    
    await nextTick()
    initCharts()
  } catch (error) {
    ElMessage.error('获取仪表盘数据失败')
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  if (!dashboardData.value) return
  
  initStatusChart()
  initProjectChart()
  initTrendChart()
}

// 需求状态分布图表
const initStatusChart = () => {
  if (!statusChartRef.value || !dashboardData.value) return
  
  const chart = echarts.init(statusChartRef.value)
  const data = dashboardData.value.requirement_status_distribution.map(item => ({
    name: item.label,
    value: item.count
  }))
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '需求状态',
        type: 'pie',
        radius: '50%',
        data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

// 项目需求分布图表
const initProjectChart = () => {
  if (!projectChartRef.value || !dashboardData.value) return
  
  const chart = echarts.init(projectChartRef.value)
  const data = dashboardData.value.project_requirement_distribution
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.project_name),
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '需求数量',
        type: 'bar',
        data: data.map(item => item.requirement_count),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  }
  
  chart.setOption(option)
}

// 趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value || !dashboardData.value) return
  
  const chart = echarts.init(trendChartRef.value)
  const trends = dashboardData.value.recent_trends
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['项目', '需求']
    },
    xAxis: {
      type: 'category',
      data: trends.projects.map(item => dayjs(item.date).format('MM-DD'))
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '项目',
        type: 'line',
        data: trends.projects.map(item => item.count),
        smooth: true
      },
      {
        name: '需求',
        type: 'line',
        data: trends.requirements.map(item => item.count),
        smooth: true
      }
    ]
  }
  
  chart.setOption(option)
}

// 获取活动图标
const getActivityIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    project_created: 'FolderAdd',
    requirement_created: 'DocumentAdd',
    requirement_updated: 'Edit'
  }
  return iconMap[type] || 'InfoFilled'
}

// 格式化时间
const formatTime = (timestamp: string) => {
  return dayjs(timestamp).fromNow()
}

// 刷新数据
const refreshData = () => {
  fetchDashboardData()
}

onMounted(() => {
  fetchDashboardData()
})
</script>

<style lang="scss" scoped>
.dashboard {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    
    .stat-card {
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      border-radius: 12px;
      padding: 24px;
      display: flex;
      align-items: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
      }
      
      .stat-icon {
        width: 64px;
        height: 64px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        
        &.primary { background: linear-gradient(135deg, #409eff, #66b3ff); color: white; }
        &.success { background: linear-gradient(135deg, #67c23a, #85ce61); color: white; }
        &.warning { background: linear-gradient(135deg, #e6a23c, #ebb563); color: white; }
        &.info { background: linear-gradient(135deg, #909399, #a6a9ad); color: white; }
      }
      
      .stat-content {
        .stat-value {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          line-height: 1;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
  
  .charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    
    .chart-card {
      &.full-width {
        grid-column: 1 / -1;
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .chart-container {
        height: 300px;
      }
    }
  }
  
  .activity-card {
    .activity-list {
      .activity-item {
        display: flex;
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .activity-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #f0f9ff;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          color: #409eff;
        }
        
        .activity-content {
          flex: 1;
          
          .activity-title {
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .activity-desc {
            font-size: 14px;
            color: #606266;
            margin-bottom: 4px;
          }
          
          .activity-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
}
</style>
