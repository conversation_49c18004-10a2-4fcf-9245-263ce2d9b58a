<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-content">
        <div class="error-icon">
          <el-icon size="120" color="#409eff">
            <WarningFilled />
          </el-icon>
        </div>
        
        <h1 class="error-title">404</h1>
        <p class="error-subtitle">页面不存在</p>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移除。
        </p>
        
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <el-icon><HomeFilled /></el-icon>
            返回首页
          </el-button>
          <el-button @click="goBack">
            <el-icon><Back /></el-icon>
            返回上页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/dashboard')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.error-page {
  min-height: 100vh;
  background: var(--app-bg-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  
  .error-container {
    text-align: center;
    padding: 40px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    
    .error-content {
      .error-icon {
        margin-bottom: 24px;
      }
      
      .error-title {
        font-size: 72px;
        font-weight: 700;
        color: #409eff;
        margin: 0 0 16px 0;
        line-height: 1;
      }
      
      .error-subtitle {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 16px 0;
      }
      
      .error-description {
        font-size: 16px;
        color: #606266;
        margin: 0 0 32px 0;
        line-height: 1.6;
      }
      
      .error-actions {
        display: flex;
        gap: 16px;
        justify-content: center;
      }
    }
  }
}
</style>
