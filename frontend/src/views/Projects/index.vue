<template>
  <div class="projects-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">项目管理</h1>
        <p class="page-desc">管理和维护项目信息</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建项目
        </el-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="项目名称">
          <el-input 
            v-model="searchForm.name" 
            placeholder="请输入项目名称"
            clearable
            @clear="handleSearch"
          />
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 项目列表 -->
    <el-card class="table-card">
      <el-table 
        :data="projectList" 
        v-loading="loading"
        stripe
        @sort-change="handleSortChange"
      >
        <el-table-column prop="project_code" label="项目编号" width="120" />
        <el-table-column prop="name" label="项目名称" min-width="200" />
        <el-table-column prop="content" label="项目内容" min-width="300" show-overflow-tooltip />
        <el-table-column prop="created_at" label="创建时间" width="180" sortable="custom">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingProject ? '编辑项目' : '新建项目'"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="projectForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="项目编号" prop="project_code">
          <el-input 
            v-model="projectForm.project_code" 
            placeholder="请输入项目编号"
            :disabled="!!editingProject"
          />
        </el-form-item>
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="projectForm.name" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="项目内容" prop="content">
          <el-input
            v-model="projectForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入项目内容描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ editingProject ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { projectApi, type Project, type ProjectCreate, type ProjectUpdate } from '@/api/projects'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showCreateDialog = ref(false)
const editingProject = ref<Project | null>(null)
const projectList = ref<Project[]>([])
const formRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  name: '',
  dateRange: [] as string[]
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 项目表单
const projectForm = reactive<ProjectCreate>({
  project_code: '',
  name: '',
  content: ''
})

// 表单验证规则
const formRules: FormRules = {
  project_code: [
    { required: true, message: '请输入项目编号', trigger: 'blur' },
    { min: 1, max: 50, message: '项目编号长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 1, max: 200, message: '项目名称长度在 1 到 200 个字符', trigger: 'blur' }
  ]
}

// 获取项目列表
const fetchProjects = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      size: pagination.size,
      name: searchForm.name || undefined,
      created_start: searchForm.dateRange[0] || undefined,
      created_end: searchForm.dateRange[1] || undefined
    }
    
    const response = await projectApi.getList(params)
    projectList.value = response.data.items
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取项目列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchProjects()
}

// 重置搜索
const handleReset = () => {
  searchForm.name = ''
  searchForm.dateRange = []
  pagination.page = 1
  fetchProjects()
}

// 排序
const handleSortChange = ({ prop, order }: any) => {
  // 这里可以添加排序逻辑
  console.log('排序:', prop, order)
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchProjects()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchProjects()
}

// 查看项目
const handleView = (project: Project) => {
  ElMessage.info(`查看项目: ${project.name}`)
}

// 编辑项目
const handleEdit = (project: Project) => {
  editingProject.value = project
  projectForm.project_code = project.project_code
  projectForm.name = project.name
  projectForm.content = project.content || ''
  showCreateDialog.value = true
}

// 删除项目
const handleDelete = async (project: Project) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除项目 "${project.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await projectApi.delete(project.id)
    ElMessage.success('删除成功')
    fetchProjects()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    if (editingProject.value) {
      // 编辑
      const updateData: ProjectUpdate = {
        name: projectForm.name,
        content: projectForm.content
      }
      await projectApi.update(editingProject.value.id, updateData)
      ElMessage.success('更新成功')
    } else {
      // 创建
      await projectApi.create(projectForm)
      ElMessage.success('创建成功')
    }
    
    showCreateDialog.value = false
    fetchProjects()
  } catch (error) {
    ElMessage.error(editingProject.value ? '更新失败' : '创建失败')
  } finally {
    submitting.value = false
  }
}

// 对话框关闭
const handleDialogClose = () => {
  editingProject.value = null
  projectForm.project_code = ''
  projectForm.name = ''
  projectForm.content = ''
  formRef.value?.resetFields()
}

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

onMounted(() => {
  fetchProjects()
})
</script>

<style lang="scss" scoped>
.projects-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 24px;
    
    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: white;
        margin: 0 0 8px 0;
      }
      
      .page-desc {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
      }
    }
  }
  
  .search-card {
    margin-bottom: 20px;
    
    :deep(.el-form--inline .el-form-item) {
      margin-right: 24px;
    }
  }
  
  .table-card {
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}
</style>
