<template>
  <div class="requirement-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">需求分析</h1>
      <p class="page-desc">使用AI智能分析和优化需求内容</p>
    </div>

    <div class="analysis-container">
      <!-- 左侧输入区域 -->
      <div class="input-section">
        <el-card class="input-card">
          <template #header>
            <div class="card-header">
              <span>需求输入</span>
              <el-button type="primary" size="small" @click="showUploadDialog = true">
                <el-icon><Upload /></el-icon>
                导入文档
              </el-button>
            </div>
          </template>
          
          <el-form :model="requirementForm" label-width="100px">
            <el-form-item label="所属项目" required>
              <el-select 
                v-model="requirementForm.project_id" 
                placeholder="请选择项目"
                style="width: 100%"
              >
                <el-option
                  v-for="project in projectList"
                  :key="project.id"
                  :label="project.name"
                  :value="project.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="需求标题" required>
              <el-input 
                v-model="requirementForm.title" 
                placeholder="请输入需求标题"
              />
            </el-form-item>
            
            <el-form-item label="需求内容" required>
              <el-input
                v-model="requirementForm.content"
                type="textarea"
                :rows="12"
                placeholder="请输入详细的需求内容，或点击上方按钮导入文档"
                show-word-limit
                maxlength="5000"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                @click="handleAnalyze"
                :loading="analyzing"
                :disabled="!requirementForm.content.trim()"
                size="large"
                style="width: 100%"
              >
                <el-icon><MagicStick /></el-icon>
                {{ analyzing ? 'AI分析中...' : '开始AI分析' }}
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 右侧结果区域 -->
      <div class="result-section">
        <el-card class="result-card">
          <template #header>
            <div class="card-header">
              <span>分析结果</span>
              <div v-if="analysisResult">
                <el-button 
                  type="success" 
                  size="small" 
                  @click="handleSave"
                  :loading="saving"
                >
                  <el-icon><Check /></el-icon>
                  保存结果
                </el-button>
                <el-button 
                  type="warning" 
                  size="small" 
                  @click="handleReanalyze"
                >
                  <el-icon><Refresh /></el-icon>
                  重新分析
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="result-content">
            <div v-if="!analysisResult && !analyzing" class="empty-state">
              <el-icon size="64" color="#c0c4cc"><DocumentCopy /></el-icon>
              <p>请在左侧输入需求内容并点击"开始AI分析"</p>
            </div>
            
            <div v-if="analyzing" class="analyzing-state">
              <el-icon size="64" class="rotating"><Loading /></el-icon>
              <p>AI正在分析您的需求，请稍候...</p>
            </div>
            
            <div v-if="analysisResult" class="analysis-content">
              <!-- 原始需求 -->
              <div class="content-section">
                <h3>原始需求</h3>
                <div class="content-box original">
                  {{ analysisResult.original_content }}
                </div>
              </div>
              
              <!-- 分析结果 -->
              <div class="content-section">
                <h3>AI分析结果</h3>
                <div class="content-box analyzed" v-html="formatAnalysisContent(analysisResult.analyzed_content)"></div>
              </div>
              
              <!-- 改进建议 -->
              <div v-if="analysisResult.suggestions && analysisResult.suggestions.length > 0" class="content-section">
                <h3>改进建议</h3>
                <ul class="suggestions-list">
                  <li v-for="(suggestion, index) in analysisResult.suggestions" :key="index">
                    {{ suggestion }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 文档上传对话框 -->
    <el-dialog v-model="showUploadDialog" title="导入需求文档" width="500px">
      <el-upload
        ref="uploadRef"
        :auto-upload="false"
        :on-change="handleFileChange"
        :before-upload="beforeUpload"
        drag
        accept=".txt,.md,.doc,.docx"
      >
        <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 txt、md、doc、docx 格式文件，大小不超过 10MB
          </div>
        </template>
      </el-upload>
      
      <template #footer>
        <el-button @click="showUploadDialog = false">取消</el-button>
        <el-button type="primary" @click="handleUpload" :loading="uploading">
          导入
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, type UploadInstance, type UploadFile } from 'element-plus'
import { projectApi, type Project } from '@/api/projects'
import { requirementApi, type RequirementAnalysisResponse } from '@/api/requirements'

// 响应式数据
const analyzing = ref(false)
const saving = ref(false)
const uploading = ref(false)
const showUploadDialog = ref(false)
const projectList = ref<Project[]>([])
const analysisResult = ref<RequirementAnalysisResponse | null>(null)
const uploadRef = ref<UploadInstance>()
const selectedFile = ref<File | null>(null)

// 需求表单
const requirementForm = reactive({
  project_id: null as number | null,
  title: '',
  content: ''
})

// 获取项目列表
const fetchProjects = async () => {
  try {
    const response = await projectApi.getAll()
    projectList.value = response.data
  } catch (error) {
    ElMessage.error('获取项目列表失败')
  }
}

// AI分析需求
const handleAnalyze = async () => {
  if (!requirementForm.content.trim()) {
    ElMessage.warning('请输入需求内容')
    return
  }
  
  try {
    analyzing.value = true
    const response = await requirementApi.analyze({
      content: requirementForm.content,
      project_id: requirementForm.project_id || undefined
    })
    
    analysisResult.value = response.data
    ElMessage.success('分析完成')
  } catch (error) {
    ElMessage.error('分析失败，请重试')
  } finally {
    analyzing.value = false
  }
}

// 重新分析
const handleReanalyze = () => {
  analysisResult.value = null
  handleAnalyze()
}

// 保存分析结果
const handleSave = async () => {
  if (!analysisResult.value || !requirementForm.project_id || !requirementForm.title) {
    ElMessage.warning('请确保已选择项目、填写标题并完成分析')
    return
  }
  
  try {
    saving.value = true
    
    // 先创建需求
    const createData = {
      project_id: requirementForm.project_id,
      title: requirementForm.title,
      original_content: requirementForm.content,
      category: '功能需求'
    }
    
    const createResponse = await requirementApi.create(createData)
    
    // 再保存分析结果
    await requirementApi.saveAnalysis(
      createResponse.data.id,
      analysisResult.value.analyzed_content
    )
    
    ElMessage.success('保存成功')
    
    // 重置表单
    requirementForm.project_id = null
    requirementForm.title = ''
    requirementForm.content = ''
    analysisResult.value = null
    
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 文件选择
const handleFileChange = (file: UploadFile) => {
  selectedFile.value = file.raw || null
}

// 文件上传前检查
const beforeUpload = (file: File) => {
  const isValidType = ['text/plain', 'text/markdown', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10
  
  if (!isValidType) {
    ElMessage.error('只支持 txt、md、doc、docx 格式文件')
    return false
  }
  
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB')
    return false
  }
  
  return false // 阻止自动上传
}

// 处理文档上传
const handleUpload = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请选择文件')
    return
  }
  
  try {
    uploading.value = true
    const response = await requirementApi.uploadDocument(selectedFile.value)
    
    requirementForm.content = response.data.content
    showUploadDialog.value = false
    selectedFile.value = null
    uploadRef.value?.clearFiles()
    
    ElMessage.success('文档导入成功')
  } catch (error) {
    ElMessage.error('文档导入失败')
  } finally {
    uploading.value = false
  }
}

// 格式化分析内容（支持Markdown）
const formatAnalysisContent = (content: string) => {
  return content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
}

onMounted(() => {
  fetchProjects()
})
</script>

<style lang="scss" scoped>
.requirement-analysis {
  .page-header {
    margin-bottom: 24px;
    
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: white;
      margin: 0 0 8px 0;
    }
    
    .page-desc {
      color: rgba(255, 255, 255, 0.8);
      margin: 0;
    }
  }
  
  .analysis-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    height: calc(100vh - 200px);
    
    .input-section,
    .result-section {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
    
    .input-card {
      height: 100%;
      
      :deep(.el-card__body) {
        height: calc(100% - 60px);
        overflow-y: auto;
      }
    }
    
    .result-card {
      height: 100%;
      
      :deep(.el-card__body) {
        height: calc(100% - 60px);
        overflow-y: auto;
      }
      
      .result-content {
        height: 100%;
        
        .empty-state,
        .analyzing-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #909399;
          
          p {
            margin-top: 16px;
            font-size: 14px;
          }
        }
        
        .rotating {
          animation: rotate 2s linear infinite;
        }
        
        .analysis-content {
          .content-section {
            margin-bottom: 24px;
            
            h3 {
              font-size: 16px;
              font-weight: 600;
              color: #303133;
              margin-bottom: 12px;
              padding-bottom: 8px;
              border-bottom: 2px solid #e4e7ed;
            }
            
            .content-box {
              padding: 16px;
              border-radius: 8px;
              line-height: 1.6;
              
              &.original {
                background: #f8f9fa;
                border-left: 4px solid #909399;
              }
              
              &.analyzed {
                background: #f0f9ff;
                border-left: 4px solid #409eff;
                
                :deep(h1), :deep(h2), :deep(h3) {
                  margin: 16px 0 8px 0;
                  color: #303133;
                }
                
                :deep(strong) {
                  color: #409eff;
                }
              }
            }
            
            .suggestions-list {
              background: #fff7e6;
              border-left: 4px solid #e6a23c;
              padding: 16px;
              border-radius: 8px;
              
              li {
                margin-bottom: 8px;
                line-height: 1.6;
                
                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
          }
        }
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式
@media (max-width: 1200px) {
  .analysis-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    height: auto;
    
    .input-section {
      .input-card {
        height: auto;
      }
    }
    
    .result-section {
      .result-card {
        height: 600px;
      }
    }
  }
}
</style>
