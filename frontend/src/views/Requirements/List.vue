<template>
  <div class="requirements-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">需求列表</h1>
        <p class="page-desc">查看和管理已分析的需求</p>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="项目名称">
          <el-select 
            v-model="searchForm.project_id" 
            placeholder="请选择项目"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="project in projectList"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="需求标题">
          <el-input 
            v-model="searchForm.title" 
            placeholder="请输入需求标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="需求分类">
          <el-select 
            v-model="searchForm.category" 
            placeholder="请选择分类"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="category in categoryList"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select 
            v-model="searchForm.status" 
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="status in statusList"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 需求列表 -->
    <el-card class="table-card">
      <el-table 
        :data="requirementList" 
        v-loading="loading"
        stripe
        @sort-change="handleSortChange"
      >
        <el-table-column prop="title" label="需求标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="project.name" label="所属项目" width="150" />
        <el-table-column prop="category" label="分类" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.category" size="small">{{ row.category }}</el-tag>
            <span v-else class="text-placeholder">未分类</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)" size="small">
              {{ getPriorityLabel(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" sortable="custom">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="需求详情"
      width="80%"
      top="5vh"
    >
      <div v-if="selectedRequirement" class="requirement-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="需求标题">
            {{ selectedRequirement.title }}
          </el-descriptions-item>
          <el-descriptions-item label="所属项目">
            {{ selectedRequirement.project?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="分类">
            {{ selectedRequirement.category || '未分类' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedRequirement.status)">
              {{ getStatusLabel(selectedRequirement.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(selectedRequirement.priority)">
              {{ getPriorityLabel(selectedRequirement.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(selectedRequirement.created_at) }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="content-sections">
          <div class="content-section">
            <h3>原始需求内容</h3>
            <div class="content-box original">
              {{ selectedRequirement.original_content }}
            </div>
          </div>
          
          <div v-if="selectedRequirement.analyzed_content" class="content-section">
            <h3>AI分析结果</h3>
            <div class="content-box analyzed" v-html="formatContent(selectedRequirement.analyzed_content)"></div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { requirementApi, type Requirement } from '@/api/requirements'
import { projectApi, type Project } from '@/api/projects'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const showDetailDialog = ref(false)
const requirementList = ref<Requirement[]>([])
const projectList = ref<Project[]>([])
const categoryList = ref<string[]>([])
const statusList = ref<Array<{ value: string; label: string }>>([])
const selectedRequirement = ref<Requirement | null>(null)

// 搜索表单
const searchForm = reactive({
  project_id: null as number | null,
  title: '',
  category: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 获取需求列表
const fetchRequirements = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      size: pagination.size,
      project_id: searchForm.project_id || undefined,
      title: searchForm.title || undefined,
      category: searchForm.category || undefined,
      status: searchForm.status || undefined
    }
    
    const response = await requirementApi.getList(params)
    requirementList.value = response.data.items
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取需求列表失败')
  } finally {
    loading.value = false
  }
}

// 获取项目列表
const fetchProjects = async () => {
  try {
    const response = await projectApi.getAll()
    projectList.value = response.data
  } catch (error) {
    ElMessage.error('获取项目列表失败')
  }
}

// 获取分类和状态列表
const fetchMetadata = async () => {
  try {
    const [categoriesRes, statusRes] = await Promise.all([
      requirementApi.getCategories(),
      requirementApi.getStatusList()
    ])
    
    categoryList.value = categoriesRes.data.categories
    statusList.value = statusRes.data.status
  } catch (error) {
    ElMessage.error('获取元数据失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchRequirements()
}

// 重置搜索
const handleReset = () => {
  searchForm.project_id = null
  searchForm.title = ''
  searchForm.category = ''
  searchForm.status = ''
  pagination.page = 1
  fetchRequirements()
}

// 排序
const handleSortChange = ({ prop, order }: any) => {
  console.log('排序:', prop, order)
}

// 分页
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchRequirements()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchRequirements()
}

// 查看详情
const handleView = (requirement: Requirement) => {
  selectedRequirement.value = requirement
  showDetailDialog.value = true
}

// 编辑需求
const handleEdit = (requirement: Requirement) => {
  ElMessage.info(`编辑需求: ${requirement.title}`)
}

// 删除需求
const handleDelete = async (requirement: Requirement) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除需求 "${requirement.title}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await requirementApi.delete(requirement.id)
    ElMessage.success('删除成功')
    fetchRequirements()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    draft: 'info',
    analyzed: 'primary',
    approved: 'success',
    in_progress: 'warning',
    completed: 'success',
    rejected: 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    draft: '草稿',
    analyzed: '已分析',
    approved: '已批准',
    in_progress: '开发中',
    completed: '已完成',
    rejected: '已拒绝'
  }
  return labelMap[status] || status
}

// 获取优先级类型
const getPriorityType = (priority: string) => {
  const typeMap: Record<string, string> = {
    low: 'info',
    medium: 'warning',
    high: 'danger'
  }
  return typeMap[priority] || 'info'
}

// 获取优先级标签
const getPriorityLabel = (priority: string) => {
  const labelMap: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高'
  }
  return labelMap[priority] || priority
}

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 格式化内容
const formatContent = (content: string) => {
  return content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
}

onMounted(() => {
  fetchRequirements()
  fetchProjects()
  fetchMetadata()
})
</script>

<style lang="scss" scoped>
.requirements-list {
  .page-header {
    margin-bottom: 24px;
    
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: white;
      margin: 0 0 8px 0;
    }
    
    .page-desc {
      color: rgba(255, 255, 255, 0.8);
      margin: 0;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
    
    :deep(.el-form--inline .el-form-item) {
      margin-right: 16px;
    }
  }
  
  .table-card {
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
  
  .requirement-detail {
    .content-sections {
      margin-top: 24px;
      
      .content-section {
        margin-bottom: 24px;
        
        h3 {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 2px solid #e4e7ed;
        }
        
        .content-box {
          padding: 16px;
          border-radius: 8px;
          line-height: 1.6;
          max-height: 300px;
          overflow-y: auto;
          
          &.original {
            background: #f8f9fa;
            border-left: 4px solid #909399;
          }
          
          &.analyzed {
            background: #f0f9ff;
            border-left: 4px solid #409eff;
            
            :deep(h1), :deep(h2), :deep(h3) {
              margin: 16px 0 8px 0;
              color: #303133;
            }
            
            :deep(strong) {
              color: #409eff;
            }
          }
        }
      }
    }
  }
}
</style>
