import api, { PaginatedResponse } from './index'

// 需求相关类型定义
export interface Requirement {
  id: number
  project_id: number
  title: string
  original_content: string
  analyzed_content?: string
  category?: string
  priority: string
  status: string
  created_at: string
  updated_at: string
  project?: {
    id: number
    name: string
    project_code: string
  }
}

export interface RequirementCreate {
  project_id: number
  title: string
  original_content: string
  category?: string
  priority?: string
}

export interface RequirementUpdate {
  title?: string
  original_content?: string
  analyzed_content?: string
  category?: string
  priority?: string
  status?: string
}

export interface RequirementQuery {
  page?: number
  size?: number
  project_id?: number
  title?: string
  category?: string
  status?: string
  created_start?: string
  created_end?: string
}

export interface RequirementAnalysisRequest {
  content: string
  project_id?: number
}

export interface RequirementAnalysisResponse {
  original_content: string
  analyzed_content: string
  suggestions?: string[]
}

export interface RequirementCategory {
  value: string
  label: string
}

export interface RequirementStatus {
  value: string
  label: string
}

// 需求API
export const requirementApi = {
  // 创建需求
  create(data: RequirementCreate) {
    return api.post<Requirement>('/requirements/', data)
  },

  // 获取需求列表（分页）
  getList(params: RequirementQuery = {}) {
    return api.get<PaginatedResponse<Requirement>>('/requirements/', { params })
  },

  // 获取单个需求
  getById(id: number) {
    return api.get<Requirement>(`/requirements/${id}`)
  },

  // 更新需求
  update(id: number, data: RequirementUpdate) {
    return api.put<Requirement>(`/requirements/${id}`, data)
  },

  // 删除需求
  delete(id: number) {
    return api.delete(`/requirements/${id}`)
  },

  // AI分析需求
  analyze(data: RequirementAnalysisRequest) {
    return api.post<RequirementAnalysisResponse>('/requirements/analyze', data)
  },

  // 保存分析结果
  saveAnalysis(id: number, analyzedContent: string) {
    return api.post(`/requirements/${id}/save-analysis`, {
      analyzed_content: analyzedContent
    })
  },

  // 上传需求文档
  uploadDocument(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    
    return api.post<{
      filename: string
      content: string
      message: string
    }>('/requirements/upload-document', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取需求分类列表
  getCategories() {
    return api.get<{ categories: string[] }>('/requirements/categories/list')
  },

  // 获取需求状态列表
  getStatusList() {
    return api.get<{ status: RequirementStatus[] }>('/requirements/status/list')
  }
}
