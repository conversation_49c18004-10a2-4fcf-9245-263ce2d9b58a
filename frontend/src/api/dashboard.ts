import api from './index'

// 仪表盘相关类型定义
export interface DashboardStats {
  basic_stats: {
    total_projects: number
    total_requirements: number
    avg_requirements_per_project: number
  }
  requirement_status_distribution: Array<{
    status: string
    count: number
    label: string
  }>
  requirement_category_distribution: Array<{
    category: string
    count: number
  }>
  recent_trends: {
    projects: Array<{
      date: string
      count: number
    }>
    requirements: Array<{
      date: string
      count: number
    }>
  }
  project_requirement_distribution: Array<{
    project_name: string
    requirement_count: number
  }>
  active_projects: Array<{
    project_name: string
    recent_requirements: number
  }>
}

export interface RecentActivity {
  type: string
  title: string
  description: string
  timestamp: string
  entity_id: number
  entity_type: string
  project_name?: string
}

export interface RecentActivities {
  activities: RecentActivity[]
}

export interface PerformanceMetrics {
  analysis_efficiency: {
    rate: number
    analyzed_count: number
    total_count: number
  }
  average_processing_time: {
    hours: number
    description: string
  }
  project_activity: {
    rate: number
    active_count: number
    total_count: number
  }
}

// 仪表盘API
export const dashboardApi = {
  // 获取仪表盘统计数据
  getStats() {
    return api.get<DashboardStats>('/dashboard/stats')
  },

  // 获取最近活动
  getRecentActivities(limit: number = 10) {
    return api.get<RecentActivities>('/dashboard/recent-activities', {
      params: { limit }
    })
  },

  // 获取性能指标
  getPerformanceMetrics() {
    return api.get<PerformanceMetrics>('/dashboard/performance-metrics')
  }
}
