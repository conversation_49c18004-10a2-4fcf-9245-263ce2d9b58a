import api, { PaginatedResponse } from './index'

// 项目相关类型定义
export interface Project {
  id: number
  project_code: string
  name: string
  content?: string
  created_at: string
  updated_at: string
}

export interface ProjectCreate {
  project_code: string
  name: string
  content?: string
}

export interface ProjectUpdate {
  name?: string
  content?: string
}

export interface ProjectQuery {
  page?: number
  size?: number
  name?: string
  created_start?: string
  created_end?: string
}

export interface ProjectStats {
  project_id: number
  project_name: string
  total_requirements: number
  draft_requirements: number
  analyzed_requirements: number
  completed_requirements: number
  created_at: string
  updated_at: string
}

// 项目API
export const projectApi = {
  // 创建项目
  create(data: ProjectCreate) {
    return api.post<Project>('/projects/', data)
  },

  // 获取项目列表（分页）
  getList(params: ProjectQuery = {}) {
    return api.get<PaginatedResponse<Project>>('/projects/', { params })
  },

  // 获取所有项目（不分页）
  getAll() {
    return api.get<Project[]>('/projects/all')
  },

  // 获取单个项目
  getById(id: number) {
    return api.get<Project>(`/projects/${id}`)
  },

  // 更新项目
  update(id: number, data: ProjectUpdate) {
    return api.put<Project>(`/projects/${id}`, data)
  },

  // 删除项目
  delete(id: number) {
    return api.delete(`/projects/${id}`)
  },

  // 获取项目统计信息
  getStats(id: number) {
    return api.get<ProjectStats>(`/projects/${id}/stats`)
  }
}
