<template>
  <div id="app" class="app-container">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

onMounted(() => {
  // 应用初始化逻辑
  console.log('智能需求管理系统启动')
})
</script>

<style lang="scss">
.app-container {
  min-height: 100vh;
  background: var(--app-bg-gradient);
}

:root {
  --app-bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  
  --border-light: #ebeef5;
  --border-lighter: #f2f6fc;
  --border-extra-light: #fafafa;
  
  --bg-white: #ffffff;
  --bg-light: #f8f9fa;
  --bg-lighter: #fafbfc;
  
  --shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --shadow-dark: 0 4px 8px rgba(0, 0, 0, 0.12), 0 0 12px rgba(0, 0, 0, 0.04);
  
  --border-radius-small: 4px;
  --border-radius-base: 6px;
  --border-radius-large: 8px;
  
  --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--app-bg-gradient);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--border-lighter);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--border-light);
  border-radius: 4px;
  
  &:hover {
    background: var(--text-placeholder);
  }
}

// Element Plus 样式覆盖
.el-card {
  border: none;
  box-shadow: var(--shadow-light);
  border-radius: var(--border-radius-large);
  
  .el-card__header {
    border-bottom: 1px solid var(--border-lighter);
    padding: 20px;
  }
  
  .el-card__body {
    padding: 20px;
  }
}

.el-button {
  border-radius: var(--border-radius-base);
  transition: var(--transition-fast);
  
  &.is-plain {
    background: var(--bg-white);
  }
}

.el-input {
  .el-input__wrapper {
    border-radius: var(--border-radius-base);
    transition: var(--transition-fast);
  }
}

.el-select {
  .el-input__wrapper {
    border-radius: var(--border-radius-base);
  }
}

.el-table {
  border-radius: var(--border-radius-large);
  overflow: hidden;
  
  .el-table__header {
    th {
      background: var(--bg-lighter);
      color: var(--text-regular);
      font-weight: 600;
    }
  }
}

.el-pagination {
  .el-pager li {
    border-radius: var(--border-radius-small);
    margin: 0 2px;
  }
  
  .btn-prev,
  .btn-next {
    border-radius: var(--border-radius-small);
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}
</style>
