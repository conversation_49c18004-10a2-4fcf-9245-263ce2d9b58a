@import './variables.scss';

// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-center { display: flex; justify-content: center; align-items: center; }
.flex-between { display: flex; justify-content: space-between; align-items: center; }
.flex-column { display: flex; flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1; }

.w-full { width: 100%; }
.h-full { height: 100%; }

// 间距工具类
@each $size in (xs, sm, md, lg, xl, xxl) {
  .m-#{$size} { margin: var(--spacing-#{$size}); }
  .mt-#{$size} { margin-top: var(--spacing-#{$size}); }
  .mr-#{$size} { margin-right: var(--spacing-#{$size}); }
  .mb-#{$size} { margin-bottom: var(--spacing-#{$size}); }
  .ml-#{$size} { margin-left: var(--spacing-#{$size}); }
  .mx-#{$size} { margin-left: var(--spacing-#{$size}); margin-right: var(--spacing-#{$size}); }
  .my-#{$size} { margin-top: var(--spacing-#{$size}); margin-bottom: var(--spacing-#{$size}); }
  
  .p-#{$size} { padding: var(--spacing-#{$size}); }
  .pt-#{$size} { padding-top: var(--spacing-#{$size}); }
  .pr-#{$size} { padding-right: var(--spacing-#{$size}); }
  .pb-#{$size} { padding-bottom: var(--spacing-#{$size}); }
  .pl-#{$size} { padding-left: var(--spacing-#{$size}); }
  .px-#{$size} { padding-left: var(--spacing-#{$size}); padding-right: var(--spacing-#{$size}); }
  .py-#{$size} { padding-top: var(--spacing-#{$size}); padding-bottom: var(--spacing-#{$size}); }
}

// 渐变背景类
.gradient-primary {
  background: $gradient-primary;
}

.gradient-secondary {
  background: $gradient-secondary;
}

.gradient-success {
  background: $gradient-success;
}

.gradient-warning {
  background: $gradient-warning;
}

.gradient-danger {
  background: $gradient-danger;
}

// 卡片样式
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: $border-radius-large;
  box-shadow: $shadow-light;
}

.modern-card {
  background: $bg-white;
  border-radius: $border-radius-large;
  box-shadow: $shadow-base;
  border: none;
  transition: $transition-base;
  
  &:hover {
    box-shadow: $shadow-dark;
    transform: translateY(-2px);
  }
}

// 按钮样式增强
.btn-gradient {
  background: $gradient-primary;
  border: none;
  color: white;
  border-radius: $border-radius-base;
  transition: $transition-base;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: $shadow-base;
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(30px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-30px);
  opacity: 0;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: $border-lighter;
  border-radius: $border-radius-small;
}

::-webkit-scrollbar-thumb {
  background: $border-light;
  border-radius: $border-radius-small;
  
  &:hover {
    background: $text-placeholder;
  }
}

// 响应式
@media (max-width: $breakpoint-md) {
  .hidden-md-and-down {
    display: none !important;
  }
}

@media (max-width: $breakpoint-sm) {
  .hidden-sm-and-down {
    display: none !important;
  }
}
