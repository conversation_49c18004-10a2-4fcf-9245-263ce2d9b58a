// 颜色变量
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

// Gemini风格的渐变色
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
$gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$gradient-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
$gradient-danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

// 文本颜色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #c0c4cc;
$text-white: #ffffff;

// 背景颜色
$bg-white: #ffffff;
$bg-light: #f8f9fa;
$bg-lighter: #fafbfc;
$bg-dark: #1a1a1a;
$bg-darker: #0f0f0f;

// 边框颜色
$border-light: #ebeef5;
$border-lighter: #f2f6fc;
$border-extra-light: #fafafa;

// 阴影
$shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$shadow-dark: 0 4px 8px rgba(0, 0, 0, 0.12), 0 0 12px rgba(0, 0, 0, 0.04);
$shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.15);

// 圆角
$border-radius-small: 4px;
$border-radius-base: 6px;
$border-radius-large: 8px;
$border-radius-round: 50%;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;
$font-size-title: 32px;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 过渡动画
$transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
$transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
$transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

// 布局
$header-height: 60px;
$sidebar-width: 240px;
$sidebar-collapsed-width: 64px;

// 响应式断点
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1920px;
